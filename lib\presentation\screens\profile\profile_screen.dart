import 'package:flutter/material.dart';
import '../../../core/constants/app_constants.dart';
import '../../../data/repositories/mock_data.dart';
import '../../../data/models/user.dart';
import '../../../data/models/track.dart';
import '../../widgets/common/user_avatar.dart';
import '../../widgets/common/track_card.dart';
import '../../widgets/common/custom_button.dart';

class ProfileScreen extends StatefulWidget {
  final String? userId;

  const ProfileScreen({super.key, this.userId});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  User? _user;
  List<Track> _userTracks = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadUserData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _loadUserData() async {
    // Simulate loading delay
    await Future.delayed(const Duration(milliseconds: 500));

    setState(() {
      _user = MockData.mockUsers.first; // For demo, use first user
      _userTracks = MockData.mockTracks.where((track) => track.artistId == _user!.id).toList();
      _isLoading = false;
    });
  }

  void _openSettings() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => _buildSettingsBottomSheet(),
    );
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(
        body: Center(child: CircularProgressIndicator()),
      );
    }

    if (_user == null) {
      return const Scaffold(
        body: Center(child: Text('User not found')),
      );
    }

    return Scaffold(
      body: NestedScrollView(
        headerSliverBuilder: (context, innerBoxIsScrolled) {
          return [
            SliverAppBar(
              expandedHeight: 300,
              pinned: true,
              actions: [
                // Settings button in app bar
                IconButton(
                  onPressed: _openSettings,
                  icon: const Icon(Icons.settings),
                  tooltip: 'Settings',
                ),
              ],
              flexibleSpace: FlexibleSpaceBar(
                background: _buildProfileHeader(),
              ),
            ),
          ];
        },
        body: Column(
          children: [
            TabBar(
              controller: _tabController,
              tabs: const [
                Tab(text: 'Tracks'),
                Tab(text: 'Playlists'),
                Tab(text: 'Likes'),
              ],
            ),
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  _buildTracksTab(),
                  _buildPlaylistsTab(),
                  _buildLikesTab(),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProfileHeader() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Theme.of(context).colorScheme.primary.withOpacity(0.8),
            Theme.of(context).colorScheme.surface,
          ],
        ),
      ),
      child: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          child: Column(
            children: [
              const Spacer(),
              UserAvatar(
                imageUrl: _user!.avatarUrl,
                size: 100,
              ),
              const SizedBox(height: 16),
              Text(
                _user!.displayName,
                style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                '@${_user!.username}',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: Colors.white.withOpacity(0.8),
                ),
              ),
              if (_user!.bio != null) ...[
                const SizedBox(height: 8),
                Text(
                  _user!.bio!,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.white.withOpacity(0.9),
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
              const SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  _buildStatColumn('Tracks', _user!.formattedTracksCount),
                  _buildStatColumn('Followers', _user!.formattedFollowersCount),
                  _buildStatColumn('Following', _user!.formattedFollowingCount),
                ],
              ),
              const SizedBox(height: 16),
              if (widget.userId != null) // Not current user
                FollowButton(
                  isFollowing: _user!.isFollowing,
                  onPressed: () {
                    setState(() {
                      _user = _user!.copyWith(
                        isFollowing: !_user!.isFollowing,
                        followersCount: _user!.isFollowing
                            ? _user!.followersCount - 1
                            : _user!.followersCount + 1,
                      );
                    });
                  },
                )
              else // Current user - show settings button
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    ElevatedButton.icon(
                      onPressed: _openSettings,
                      icon: const Icon(Icons.settings, size: 18),
                      label: const Text('Settings'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.white.withOpacity(0.2),
                        foregroundColor: Colors.white,
                        elevation: 0,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(20),
                          side: BorderSide(color: Colors.white.withOpacity(0.3)),
                        ),
                      ),
                    ),
                  ],
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatColumn(String label, String value) {
    return Column(
      children: [
        Text(
          value,
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        Text(
          label,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: Colors.white.withOpacity(0.8),
          ),
        ),
      ],
    );
  }

  Widget _buildTracksTab() {
    if (_userTracks.isEmpty) {
      return const Center(
        child: Text('No tracks yet'),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.only(bottom: 80),
      itemCount: _userTracks.length,
      itemBuilder: (context, index) {
        final track = _userTracks[index];
        return TrackCard(
          track: track,
          onTap: () {},
          onPlay: () {},
          onLike: () {},
          onRepost: () {},
          onComment: () {},
        );
      },
    );
  }

  Widget _buildPlaylistsTab() {
    return const Center(
      child: Text('Playlists coming soon'),
    );
  }

  Widget _buildLikesTab() {
    return const Center(
      child: Text('Liked tracks coming soon'),
    );
  }

  Widget _buildSettingsBottomSheet() {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Handle bar
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.symmetric(vertical: 12),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.3),
                borderRadius: BorderRadius.circular(2),
              ),
            ),

            // Settings header
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
              child: Row(
                children: [
                  Text(
                    'Settings',
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    onPressed: () => Navigator.pop(context),
                    icon: const Icon(Icons.close),
                  ),
                ],
              ),
            ),

            const Divider(height: 1),

            // Settings options
            _buildSettingsOption(
              icon: Icons.person_outline,
              title: 'Edit Profile',
              subtitle: 'Update your profile information',
              onTap: () {
                Navigator.pop(context);
                // TODO: Navigate to edit profile
              },
            ),

            _buildSettingsOption(
              icon: Icons.notifications_outlined,
              title: 'Notifications',
              subtitle: 'Manage your notification preferences',
              onTap: () {
                Navigator.pop(context);
                // TODO: Navigate to notifications settings
              },
            ),

            _buildSettingsOption(
              icon: Icons.security_outlined,
              title: 'Privacy & Security',
              subtitle: 'Control your privacy settings',
              onTap: () {
                Navigator.pop(context);
                // TODO: Navigate to privacy settings
              },
            ),

            _buildSettingsOption(
              icon: Icons.palette_outlined,
              title: 'Appearance',
              subtitle: 'Customize app theme and display',
              onTap: () {
                Navigator.pop(context);
                // TODO: Navigate to appearance settings
              },
            ),

            _buildSettingsOption(
              icon: Icons.help_outline,
              title: 'Help & Support',
              subtitle: 'Get help and contact support',
              onTap: () {
                Navigator.pop(context);
                // TODO: Navigate to help
              },
            ),

            _buildSettingsOption(
              icon: Icons.info_outline,
              title: 'About',
              subtitle: 'App version and information',
              onTap: () {
                Navigator.pop(context);
                // TODO: Show about dialog
              },
            ),

            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  Widget _buildSettingsOption({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return ListTile(
      leading: Container(
        width: 40,
        height: 40,
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
          borderRadius: BorderRadius.circular(10),
        ),
        child: Icon(
          icon,
          color: Theme.of(context).colorScheme.primary,
          size: 22,
        ),
      ),
      title: Text(
        title,
        style: Theme.of(context).textTheme.titleMedium?.copyWith(
          fontWeight: FontWeight.w600,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: Theme.of(context).textTheme.bodySmall?.copyWith(
          color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
        ),
      ),
      trailing: const Icon(Icons.chevron_right),
      onTap: onTap,
      contentPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 4),
    );
  }
}
