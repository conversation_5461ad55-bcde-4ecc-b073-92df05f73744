import 'package:flutter/material.dart';
import '../../../core/constants/app_constants.dart';
import '../../../data/repositories/mock_data.dart';
import '../../../data/models/user.dart';
import '../../../data/models/track.dart';
import '../../widgets/common/user_avatar.dart';
import '../../widgets/common/track_card.dart';
import '../../widgets/common/custom_button.dart';

class ProfileScreen extends StatefulWidget {
  final String? userId;

  const ProfileScreen({super.key, this.userId});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  User? _user;
  List<Track> _userTracks = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadUserData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _loadUserData() async {
    // Simulate loading delay
    await Future.delayed(const Duration(milliseconds: 500));

    setState(() {
      _user = MockData.mockUsers.first; // For demo, use first user
      _userTracks = MockData.mockTracks.where((track) => track.artistId == _user!.id).toList();
      _isLoading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(
        body: Center(child: CircularProgressIndicator()),
      );
    }

    if (_user == null) {
      return const Scaffold(
        body: Center(child: Text('User not found')),
      );
    }

    return Scaffold(
      body: NestedScrollView(
        headerSliverBuilder: (context, innerBoxIsScrolled) {
          return [
            SliverAppBar(
              expandedHeight: 300,
              pinned: true,
              flexibleSpace: FlexibleSpaceBar(
                background: _buildProfileHeader(),
              ),
            ),
          ];
        },
        body: Column(
          children: [
            TabBar(
              controller: _tabController,
              tabs: const [
                Tab(text: 'Tracks'),
                Tab(text: 'Playlists'),
                Tab(text: 'Likes'),
              ],
            ),
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  _buildTracksTab(),
                  _buildPlaylistsTab(),
                  _buildLikesTab(),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProfileHeader() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Theme.of(context).colorScheme.primary.withOpacity(0.8),
            Theme.of(context).colorScheme.surface,
          ],
        ),
      ),
      child: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          child: Column(
            children: [
              const Spacer(),
              UserAvatar(
                imageUrl: _user!.avatarUrl,
                size: 100,
              ),
              const SizedBox(height: 16),
              Text(
                _user!.displayName,
                style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                '@${_user!.username}',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: Colors.white.withOpacity(0.8),
                ),
              ),
              if (_user!.bio != null) ...[
                const SizedBox(height: 8),
                Text(
                  _user!.bio!,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.white.withOpacity(0.9),
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
              const SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  _buildStatColumn('Tracks', _user!.formattedTracksCount),
                  _buildStatColumn('Followers', _user!.formattedFollowersCount),
                  _buildStatColumn('Following', _user!.formattedFollowingCount),
                ],
              ),
              const SizedBox(height: 16),
              if (widget.userId != null) // Not current user
                FollowButton(
                  isFollowing: _user!.isFollowing,
                  onPressed: () {
                    setState(() {
                      _user = _user!.copyWith(
                        isFollowing: !_user!.isFollowing,
                        followersCount: _user!.isFollowing 
                            ? _user!.followersCount - 1 
                            : _user!.followersCount + 1,
                      );
                    });
                  },
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatColumn(String label, String value) {
    return Column(
      children: [
        Text(
          value,
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        Text(
          label,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: Colors.white.withOpacity(0.8),
          ),
        ),
      ],
    );
  }

  Widget _buildTracksTab() {
    if (_userTracks.isEmpty) {
      return const Center(
        child: Text('No tracks yet'),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.only(bottom: 80),
      itemCount: _userTracks.length,
      itemBuilder: (context, index) {
        final track = _userTracks[index];
        return TrackCard(
          track: track,
          onTap: () {},
          onPlay: () {},
          onLike: () {},
          onRepost: () {},
          onComment: () {},
        );
      },
    );
  }

  Widget _buildPlaylistsTab() {
    return const Center(
      child: Text('Playlists coming soon'),
    );
  }

  Widget _buildLikesTab() {
    return const Center(
      child: Text('Liked tracks coming soon'),
    );
  }
}
