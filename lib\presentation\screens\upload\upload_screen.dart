import 'package:flutter/material.dart';
import '../../../core/constants/app_constants.dart';
import '../../widgets/common/custom_button.dart';

class UploadScreen extends StatefulWidget {
  const UploadScreen({super.key});

  @override
  State<UploadScreen> createState() => _UploadScreenState();
}

class _UploadScreenState extends State<UploadScreen> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _tagsController = TextEditingController();
  
  String? _selectedGenre;
  String? _selectedAudioFile;
  String? _selectedArtwork;
  bool _isUploading = false;

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    _tagsController.dispose();
    super.dispose();
  }

  void _selectAudioFile() async {
    // Simulate file picker
    await Future.delayed(const Duration(milliseconds: 500));
    setState(() {
      _selectedAudioFile = 'demo_track.mp3';
    });
  }

  void _selectArtwork() async {
    // Simulate image picker
    await Future.delayed(const Duration(milliseconds: 500));
    setState(() {
      _selectedArtwork = 'artwork.jpg';
    });
  }

  void _uploadTrack() async {
    if (_formKey.currentState?.validate() ?? false) {
      if (_selectedAudioFile == null) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Please select an audio file')),
        );
        return;
      }

      setState(() {
        _isUploading = true;
      });

      // Simulate upload
      await Future.delayed(const Duration(seconds: 3));

      if (mounted) {
        setState(() {
          _isUploading = false;
        });
        
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Track uploaded successfully!')),
        );
        
        // Clear form
        _formKey.currentState?.reset();
        _titleController.clear();
        _descriptionController.clear();
        _tagsController.clear();
        setState(() {
          _selectedGenre = null;
          _selectedAudioFile = null;
          _selectedArtwork = null;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(AppStrings.uploadTrack),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Audio file selection
              Card(
                child: InkWell(
                  onTap: _selectAudioFile,
                  borderRadius: BorderRadius.circular(AppConstants.borderRadius),
                  child: Container(
                    height: 120,
                    padding: const EdgeInsets.all(AppConstants.defaultPadding),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          _selectedAudioFile != null ? Icons.audio_file : Icons.upload_file,
                          size: 48,
                          color: Theme.of(context).colorScheme.primary,
                        ),
                        const SizedBox(height: 8),
                        Text(
                          _selectedAudioFile ?? AppStrings.selectFile,
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            color: _selectedAudioFile != null 
                                ? Theme.of(context).colorScheme.primary
                                : null,
                          ),
                        ),
                        if (_selectedAudioFile == null)
                          Text(
                            'Tap to select audio file',
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                            ),
                          ),
                      ],
                    ),
                  ),
                ),
              ),
              
              const SizedBox(height: 24),
              
              // Track title
              TextFormField(
                controller: _titleController,
                decoration: const InputDecoration(
                  labelText: AppStrings.trackTitle,
                  prefixIcon: Icon(Icons.music_note),
                ),
                validator: (value) {
                  if (value?.isEmpty ?? true) {
                    return 'Please enter a track title';
                  }
                  return null;
                },
              ),
              
              const SizedBox(height: 16),
              
              // Description
              TextFormField(
                controller: _descriptionController,
                decoration: const InputDecoration(
                  labelText: AppStrings.description,
                  prefixIcon: Icon(Icons.description),
                ),
                maxLines: 3,
              ),
              
              const SizedBox(height: 16),
              
              // Genre dropdown
              DropdownButtonFormField<String>(
                value: _selectedGenre,
                decoration: const InputDecoration(
                  labelText: AppStrings.genre,
                  prefixIcon: Icon(Icons.category),
                ),
                items: const [
                  'Electronic',
                  'Hip-Hop',
                  'Indie Pop',
                  'Rock',
                  'Jazz',
                  'Classical',
                  'Ambient',
                  'House',
                  'Techno',
                  'R&B',
                ].map((genre) {
                  return DropdownMenuItem(
                    value: genre,
                    child: Text(genre),
                  );
                }).toList(),
                onChanged: (value) {
                  setState(() {
                    _selectedGenre = value;
                  });
                },
                validator: (value) {
                  if (value == null) {
                    return 'Please select a genre';
                  }
                  return null;
                },
              ),
              
              const SizedBox(height: 16),
              
              // Tags
              TextFormField(
                controller: _tagsController,
                decoration: const InputDecoration(
                  labelText: AppStrings.tags,
                  prefixIcon: Icon(Icons.tag),
                  hintText: 'e.g., chill, ambient, electronic',
                ),
              ),
              
              const SizedBox(height: 24),
              
              // Artwork selection
              Card(
                child: InkWell(
                  onTap: _selectArtwork,
                  borderRadius: BorderRadius.circular(AppConstants.borderRadius),
                  child: Container(
                    height: 100,
                    padding: const EdgeInsets.all(AppConstants.defaultPadding),
                    child: Row(
                      children: [
                        Icon(
                          _selectedArtwork != null ? Icons.image : Icons.add_photo_alternate,
                          size: 32,
                          color: Theme.of(context).colorScheme.primary,
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Text(
                                _selectedArtwork ?? AppStrings.artwork,
                                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                  color: _selectedArtwork != null 
                                      ? Theme.of(context).colorScheme.primary
                                      : null,
                                ),
                              ),
                              Text(
                                _selectedArtwork != null 
                                    ? 'Tap to change artwork'
                                    : 'Tap to add artwork (optional)',
                                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                  color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              
              const SizedBox(height: 32),
              
              // Upload buttons
              Row(
                children: [
                  Expanded(
                    child: CustomButton(
                      text: AppStrings.draft,
                      onPressed: _isUploading ? null : () {
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(content: Text('Saved as draft')),
                        );
                      },
                      type: ButtonType.outline,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: CustomButton(
                      text: AppStrings.publish,
                      onPressed: _isUploading ? null : _uploadTrack,
                      isLoading: _isUploading,
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: 80), // Bottom padding for mini player
            ],
          ),
        ),
      ),
    );
  }
}
