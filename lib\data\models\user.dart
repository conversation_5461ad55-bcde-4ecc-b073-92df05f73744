import 'package:equatable/equatable.dart';

class User extends Equatable {
  final String id;
  final String username;
  final String displayName;
  final String email;
  final String? bio;
  final String? avatarUrl;
  final String? bannerUrl;
  final String? location;
  final String? website;
  final int followersCount;
  final int followingCount;
  final int tracksCount;
  final int likesCount;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final bool isVerified;
  final bool isFollowing;
  final bool isFollowedBy;

  const User({
    required this.id,
    required this.username,
    required this.displayName,
    required this.email,
    this.bio,
    this.avatarUrl,
    this.bannerUrl,
    this.location,
    this.website,
    this.followersCount = 0,
    this.followingCount = 0,
    this.tracksCount = 0,
    this.likesCount = 0,
    required this.createdAt,
    this.updatedAt,
    this.isVerified = false,
    this.isFollowing = false,
    this.isFollowedBy = false,
  });

  User copyWith({
    String? id,
    String? username,
    String? displayName,
    String? email,
    String? bio,
    String? avatarUrl,
    String? bannerUrl,
    String? location,
    String? website,
    int? followersCount,
    int? followingCount,
    int? tracksCount,
    int? likesCount,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isVerified,
    bool? isFollowing,
    bool? isFollowedBy,
  }) {
    return User(
      id: id ?? this.id,
      username: username ?? this.username,
      displayName: displayName ?? this.displayName,
      email: email ?? this.email,
      bio: bio ?? this.bio,
      avatarUrl: avatarUrl ?? this.avatarUrl,
      bannerUrl: bannerUrl ?? this.bannerUrl,
      location: location ?? this.location,
      website: website ?? this.website,
      followersCount: followersCount ?? this.followersCount,
      followingCount: followingCount ?? this.followingCount,
      tracksCount: tracksCount ?? this.tracksCount,
      likesCount: likesCount ?? this.likesCount,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isVerified: isVerified ?? this.isVerified,
      isFollowing: isFollowing ?? this.isFollowing,
      isFollowedBy: isFollowedBy ?? this.isFollowedBy,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'username': username,
      'displayName': displayName,
      'email': email,
      'bio': bio,
      'avatarUrl': avatarUrl,
      'bannerUrl': bannerUrl,
      'location': location,
      'website': website,
      'followersCount': followersCount,
      'followingCount': followingCount,
      'tracksCount': tracksCount,
      'likesCount': likesCount,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
      'isVerified': isVerified,
      'isFollowing': isFollowing,
      'isFollowedBy': isFollowedBy,
    };
  }

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['id'] as String,
      username: json['username'] as String,
      displayName: json['displayName'] as String,
      email: json['email'] as String,
      bio: json['bio'] as String?,
      avatarUrl: json['avatarUrl'] as String?,
      bannerUrl: json['bannerUrl'] as String?,
      location: json['location'] as String?,
      website: json['website'] as String?,
      followersCount: json['followersCount'] as int? ?? 0,
      followingCount: json['followingCount'] as int? ?? 0,
      tracksCount: json['tracksCount'] as int? ?? 0,
      likesCount: json['likesCount'] as int? ?? 0,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] != null 
          ? DateTime.parse(json['updatedAt'] as String) 
          : null,
      isVerified: json['isVerified'] as bool? ?? false,
      isFollowing: json['isFollowing'] as bool? ?? false,
      isFollowedBy: json['isFollowedBy'] as bool? ?? false,
    );
  }

  String get formattedFollowersCount {
    if (followersCount >= 1000000) {
      return '${(followersCount / 1000000).toStringAsFixed(1)}M';
    } else if (followersCount >= 1000) {
      return '${(followersCount / 1000).toStringAsFixed(1)}K';
    }
    return followersCount.toString();
  }

  String get formattedFollowingCount {
    if (followingCount >= 1000000) {
      return '${(followingCount / 1000000).toStringAsFixed(1)}M';
    } else if (followingCount >= 1000) {
      return '${(followingCount / 1000).toStringAsFixed(1)}K';
    }
    return followingCount.toString();
  }

  String get formattedTracksCount {
    if (tracksCount >= 1000000) {
      return '${(tracksCount / 1000000).toStringAsFixed(1)}M';
    } else if (tracksCount >= 1000) {
      return '${(tracksCount / 1000).toStringAsFixed(1)}K';
    }
    return tracksCount.toString();
  }

  @override
  List<Object?> get props => [
        id,
        username,
        displayName,
        email,
        bio,
        avatarUrl,
        bannerUrl,
        location,
        website,
        followersCount,
        followingCount,
        tracksCount,
        likesCount,
        createdAt,
        updatedAt,
        isVerified,
        isFollowing,
        isFollowedBy,
      ];
}
