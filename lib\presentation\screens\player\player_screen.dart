import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../../../core/constants/app_constants.dart';
import '../../../data/repositories/mock_data.dart';
import '../../../data/models/track.dart';
import '../../widgets/common/custom_button.dart';

class PlayerScreen extends StatefulWidget {
  final String? trackId;

  const PlayerScreen({super.key, this.trackId});

  @override
  State<PlayerScreen> createState() => _PlayerScreenState();
}

class _PlayerScreenState extends State<PlayerScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _rotationAnimation;
  
  Track? _currentTrack;
  bool _isPlaying = false;
  bool _isShuffled = false;
  bool _isRepeated = false;
  double _currentPosition = 0.3; // Demo position
  
  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _loadTrack();
  }

  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: const Duration(seconds: 10),
      vsync: this,
    );

    _rotationAnimation = Tween<double>(
      begin: 0,
      end: 1,
    ).animate(_animationController);
  }

  void _loadTrack() {
    setState(() {
      _currentTrack = MockData.mockTracks.first; // For demo
    });
  }

  void _togglePlayPause() {
    setState(() {
      _isPlaying = !_isPlaying;
    });

    if (_isPlaying) {
      _animationController.repeat();
    } else {
      _animationController.stop();
    }
  }

  void _toggleShuffle() {
    setState(() {
      _isShuffled = !_isShuffled;
    });
  }

  void _toggleRepeat() {
    setState(() {
      _isRepeated = !_isRepeated;
    });
  }

  void _onSeek(double value) {
    setState(() {
      _currentPosition = value;
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (_currentTrack == null) {
      return const Scaffold(
        body: Center(child: CircularProgressIndicator()),
      );
    }

    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.background,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          onPressed: () => Navigator.of(context).pop(),
          icon: const Icon(Icons.keyboard_arrow_down, size: 32),
        ),
        title: Column(
          children: [
            Text(
              'Now Playing',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Theme.of(context).colorScheme.onBackground.withOpacity(0.7),
              ),
            ),
            Text(
              _currentTrack!.artistName,
              style: Theme.of(context).textTheme.titleSmall,
            ),
          ],
        ),
        centerTitle: true,
        actions: [
          IconButton(
            onPressed: () {},
            icon: const Icon(Icons.more_vert),
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          children: [
            const Spacer(),
            
            // Album artwork
            AnimatedBuilder(
              animation: _rotationAnimation,
              builder: (context, child) {
                return Transform.rotate(
                  angle: _rotationAnimation.value * 2 * 3.14159,
                  child: Container(
                    width: 300,
                    height: 300,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      boxShadow: [
                        BoxShadow(
                          color: Theme.of(context).colorScheme.primary.withOpacity(0.3),
                          blurRadius: 30,
                          spreadRadius: 10,
                        ),
                      ],
                    ),
                    child: ClipOval(
                      child: CachedNetworkImage(
                        imageUrl: _currentTrack!.artworkUrl ?? '',
                        fit: BoxFit.cover,
                        placeholder: (context, url) => Container(
                          color: Theme.of(context).colorScheme.surface,
                          child: const Icon(Icons.music_note, size: 100),
                        ),
                        errorWidget: (context, url, error) => Container(
                          color: Theme.of(context).colorScheme.surface,
                          child: const Icon(Icons.music_note, size: 100),
                        ),
                      ),
                    ),
                  ),
                );
              },
            ),
            
            const Spacer(),
            
            // Track info
            Text(
              _currentTrack!.title,
              style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 8),
            Text(
              _currentTrack!.artistName,
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                color: Theme.of(context).colorScheme.onBackground.withOpacity(0.7),
              ),
              textAlign: TextAlign.center,
            ),
            
            const SizedBox(height: 32),
            
            // Progress bar
            Column(
              children: [
                Slider(
                  value: _currentPosition,
                  onChanged: _onSeek,
                  activeColor: Theme.of(context).colorScheme.primary,
                  inactiveColor: Theme.of(context).colorScheme.onBackground.withOpacity(0.3),
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        '${(_currentPosition * _currentTrack!.duration.inSeconds ~/ 60).toString().padLeft(1, '0')}:${((_currentPosition * _currentTrack!.duration.inSeconds) % 60).toInt().toString().padLeft(2, '0')}',
                        style: Theme.of(context).textTheme.bodySmall,
                      ),
                      Text(
                        _currentTrack!.formattedDuration,
                        style: Theme.of(context).textTheme.bodySmall,
                      ),
                    ],
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 32),
            
            // Controls
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  onPressed: _toggleShuffle,
                  icon: Icon(
                    Icons.shuffle,
                    color: _isShuffled 
                        ? Theme.of(context).colorScheme.primary
                        : Theme.of(context).colorScheme.onBackground.withOpacity(0.7),
                  ),
                ),
                IconButton(
                  onPressed: () {},
                  icon: const Icon(Icons.skip_previous, size: 32),
                ),
                PlayButton(
                  isPlaying: _isPlaying,
                  onPressed: _togglePlayPause,
                  size: 64,
                ),
                IconButton(
                  onPressed: () {},
                  icon: const Icon(Icons.skip_next, size: 32),
                ),
                IconButton(
                  onPressed: _toggleRepeat,
                  icon: Icon(
                    Icons.repeat,
                    color: _isRepeated 
                        ? Theme.of(context).colorScheme.primary
                        : Theme.of(context).colorScheme.onBackground.withOpacity(0.7),
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 32),
            
            // Action buttons
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  onPressed: () {},
                  icon: Icon(
                    _currentTrack!.isLiked ? Icons.favorite : Icons.favorite_border,
                    color: _currentTrack!.isLiked 
                        ? Theme.of(context).colorScheme.primary
                        : null,
                  ),
                ),
                IconButton(
                  onPressed: () {},
                  icon: const Icon(Icons.comment_outlined),
                ),
                IconButton(
                  onPressed: () {},
                  icon: Icon(
                    _currentTrack!.isReposted ? Icons.repeat : Icons.repeat,
                    color: _currentTrack!.isReposted 
                        ? Theme.of(context).colorScheme.primary
                        : null,
                  ),
                ),
                IconButton(
                  onPressed: () {},
                  icon: const Icon(Icons.share_outlined),
                ),
                IconButton(
                  onPressed: () {},
                  icon: const Icon(Icons.playlist_add),
                ),
              ],
            ),
            
            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }
}
