import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../../../core/routing/app_router.dart';
import '../../../data/repositories/mock_data.dart';
import '../../../data/models/track.dart';
import '../../widgets/common/custom_button.dart';

class ImmersiveFeedScreen extends StatefulWidget {
  const ImmersiveFeedScreen({super.key});

  @override
  State<ImmersiveFeedScreen> createState() => _ImmersiveFeedScreenState();
}

class _ImmersiveFeedScreenState extends State<ImmersiveFeedScreen>
    with TickerProviderStateMixin {
  late PageController _pageController;
  late AnimationController _playButtonController;
  late Animation<double> _playButtonAnimation;
  int _currentIndex = 0;
  List<Track> _tracks = [];
  bool _isLoading = true;
  bool _isPlaying = false;

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
    _playButtonController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _playButtonAnimation = Tween<double>(
      begin: 1.0,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _playButtonController,
      curve: Curves.elasticOut,
    ));
    _loadTracks();
  }

  @override
  void dispose() {
    _pageController.dispose();
    _playButtonController.dispose();
    super.dispose();
  }

  Future<void> _loadTracks() async {
    // Simulate loading
    await Future.delayed(const Duration(milliseconds: 500));
    
    setState(() {
      _tracks = MockData.mockTracks;
      _isLoading = false;
    });
  }

  void _onPageChanged(int index) {
    setState(() {
      _currentIndex = index;
      // Auto-play when page changes
      _isPlaying = true;
    });
  }

  void _togglePlayPause() {
    setState(() {
      _isPlaying = !_isPlaying;
    });

    if (_isPlaying) {
      _playButtonController.forward();
    } else {
      _playButtonController.reverse();
    }
  }

  void _toggleLike(Track track) {
    setState(() {
      final trackIndex = _tracks.indexWhere((t) => t.id == track.id);
      if (trackIndex != -1) {
        _tracks[trackIndex] = track.copyWith(
          isLiked: !track.isLiked,
          likeCount: track.isLiked ? track.likeCount - 1 : track.likeCount + 1,
        );
      }
    });
  }

  void _toggleRepost(Track track) {
    setState(() {
      final trackIndex = _tracks.indexWhere((t) => t.id == track.id);
      if (trackIndex != -1) {
        _tracks[trackIndex] = track.copyWith(
          isReposted: !track.isReposted,
          repostCount: track.isReposted ? track.repostCount - 1 : track.repostCount + 1,
        );
      }
    });
  }

  void _showMoreOptions(Track track) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => _buildMoreOptionsSheet(track),
    );
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(
        backgroundColor: Colors.black,
        body: Center(child: CircularProgressIndicator()),
      );
    }

    return Scaffold(
      backgroundColor: Colors.black,
      body: PageView.builder(
        controller: _pageController,
        scrollDirection: Axis.vertical,
        onPageChanged: _onPageChanged,
        itemCount: _tracks.length,
        itemBuilder: (context, index) {
          final track = _tracks[index];
          return _buildTrackPage(track, index == _currentIndex);
        },
      ),
    );
  }

  Widget _buildTrackPage(Track track, bool isActive) {
    return Stack(
      fit: StackFit.expand,
      children: [
        // Background artwork with blur effect
        CachedNetworkImage(
          imageUrl: track.artworkUrl ?? '',
          fit: BoxFit.cover,
          placeholder: (context, url) => Container(
            color: Theme.of(context).colorScheme.surface,
            child: const Icon(Icons.music_note, size: 100, color: Colors.white),
          ),
          errorWidget: (context, url, error) => Container(
            color: Theme.of(context).colorScheme.surface,
            child: const Icon(Icons.music_note, size: 100, color: Colors.white),
          ),
        ),
        
        // Dark overlay for better text visibility
        Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                Colors.black.withOpacity(0.3),
                Colors.black.withOpacity(0.7),
              ],
            ),
          ),
        ),
        
        // Content overlay
        SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                // Top bar with user info
                Row(
                  children: [
                    CircleAvatar(
                      radius: 20,
                      backgroundImage: CachedNetworkImageProvider(
                        MockData.mockUsers.first.avatarUrl ?? '',
                      ),
                      backgroundColor: Colors.grey,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            track.artistName,
                            style: const TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.w600,
                              fontSize: 16,
                            ),
                          ),
                          Text(
                            '2 hours ago',
                            style: TextStyle(
                              color: Colors.white.withOpacity(0.7),
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                    ),
                    FollowButton(
                      isFollowing: false,
                      onPressed: () {},
                    ),
                  ],
                ),
                
                const Spacer(),
                
                // Bottom content area
                Row(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    // Track info and description
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            track.title,
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                            ),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                          const SizedBox(height: 8),
                          if (track.description != null) ...[
                            Text(
                              track.description!,
                              style: TextStyle(
                                color: Colors.white.withOpacity(0.9),
                                fontSize: 14,
                              ),
                              maxLines: 3,
                              overflow: TextOverflow.ellipsis,
                            ),
                            const SizedBox(height: 8),
                          ],
                          // Tags
                          if (track.tags.isNotEmpty) ...[
                            Wrap(
                              spacing: 8,
                              children: track.tags.take(3).map((tag) {
                                return Text(
                                  '#$tag',
                                  style: TextStyle(
                                    color: Theme.of(context).colorScheme.primary,
                                    fontSize: 12,
                                    fontWeight: FontWeight.w500,
                                  ),
                                );
                              }).toList(),
                            ),
                            const SizedBox(height: 16),
                          ],
                          // Stats
                          Row(
                            children: [
                              Icon(
                                Icons.play_arrow,
                                color: Colors.white.withOpacity(0.7),
                                size: 16,
                              ),
                              const SizedBox(width: 4),
                              Text(
                                track.formattedPlayCount,
                                style: TextStyle(
                                  color: Colors.white.withOpacity(0.7),
                                  fontSize: 12,
                                ),
                              ),
                              const SizedBox(width: 16),
                              Icon(
                                Icons.favorite,
                                color: Colors.white.withOpacity(0.7),
                                size: 16,
                              ),
                              const SizedBox(width: 4),
                              Text(
                                track.formattedLikeCount,
                                style: TextStyle(
                                  color: Colors.white.withOpacity(0.7),
                                  fontSize: 12,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                    
                    const SizedBox(width: 16),
                    
                    // Action buttons column
                    Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        // Play/Pause button
                        AnimatedBuilder(
                          animation: _playButtonAnimation,
                          builder: (context, child) {
                            return Transform.scale(
                              scale: _playButtonAnimation.value,
                              child: _buildActionButton(
                                icon: _isPlaying ? Icons.pause : Icons.play_arrow,
                                onTap: _togglePlayPause,
                                isLarge: true,
                              ),
                            );
                          },
                        ),
                        
                        const SizedBox(height: 20),
                        
                        // Like button
                        _buildActionButton(
                          icon: track.isLiked ? Icons.favorite : Icons.favorite_border,
                          label: track.formattedLikeCount,
                          onTap: () => _toggleLike(track),
                          color: track.isLiked ? Colors.red : Colors.white,
                        ),
                        
                        const SizedBox(height: 20),
                        
                        // Repost button
                        _buildActionButton(
                          icon: Icons.repeat,
                          label: track.repostCount.toString(),
                          onTap: () => _toggleRepost(track),
                          color: track.isReposted ? Theme.of(context).colorScheme.primary : Colors.white,
                        ),
                        
                        const SizedBox(height: 20),
                        
                        // Comment button
                        _buildActionButton(
                          icon: Icons.comment_outlined,
                          label: track.commentCount.toString(),
                          onTap: () => context.goToTrackDetail(track.id),
                        ),
                        
                        const SizedBox(height: 20),
                        
                        // Share button
                        _buildActionButton(
                          icon: Icons.share_outlined,
                          onTap: () {
                            // Handle share
                          },
                        ),
                        
                        const SizedBox(height: 20),
                        
                        // More options
                        _buildActionButton(
                          icon: Icons.more_vert,
                          onTap: () => _showMoreOptions(track),
                        ),
                      ],
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),

        // Progress indicator at bottom
        if (isActive)
          Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            child: SizedBox(
              height: 2,
              child: LinearProgressIndicator(
                value: 0.3, // Demo progress
                backgroundColor: Colors.white.withOpacity(0.3),
                valueColor: AlwaysStoppedAnimation<Color>(
                  Theme.of(context).colorScheme.primary,
                ),
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    String? label,
    required VoidCallback onTap,
    Color color = Colors.white,
    bool isLarge = false,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        children: [
          Container(
            width: isLarge ? 56 : 48,
            height: isLarge ? 56 : 48,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: isLarge ? Theme.of(context).colorScheme.primary : Colors.black.withOpacity(0.3),
              border: isLarge ? null : Border.all(color: Colors.white.withOpacity(0.3)),
            ),
            child: Icon(
              icon,
              color: isLarge ? Colors.white : color,
              size: isLarge ? 28 : 24,
            ),
          ),
          if (label != null) ...[
            const SizedBox(height: 4),
            Text(
              label,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildMoreOptionsSheet(Track track) {
    return Container(
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 40,
            height: 4,
            margin: const EdgeInsets.symmetric(vertical: 12),
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          ListTile(
            leading: const Icon(Icons.playlist_add),
            title: const Text('Add to Playlist'),
            onTap: () => Navigator.pop(context),
          ),
          ListTile(
            leading: const Icon(Icons.download_outlined),
            title: const Text('Download'),
            onTap: () => Navigator.pop(context),
          ),
          ListTile(
            leading: const Icon(Icons.person_add_outlined),
            title: const Text('Follow Artist'),
            onTap: () => Navigator.pop(context),
          ),
          ListTile(
            leading: const Icon(Icons.share_outlined),
            title: const Text('Share Track'),
            onTap: () => Navigator.pop(context),
          ),
          ListTile(
            leading: const Icon(Icons.report_outlined),
            title: const Text('Report'),
            onTap: () => Navigator.pop(context),
          ),
          const SizedBox(height: 20),
        ],
      ),
    );
  }
}
