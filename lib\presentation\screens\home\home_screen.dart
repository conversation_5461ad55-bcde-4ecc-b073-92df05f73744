import 'package:flutter/material.dart';
import '../../../core/constants/app_constants.dart';
import '../../../core/routing/app_router.dart';
import '../../../data/repositories/mock_data.dart';
import '../../../data/models/track.dart';
import '../../widgets/common/track_card.dart';
import '../../widgets/common/user_avatar.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  final ScrollController _scrollController = ScrollController();
  List<Track> _tracks = [];
  bool _isLoading = false;
  bool _hasMore = true;

  @override
  void initState() {
    super.initState();
    _loadTracks();
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >= 
        _scrollController.position.maxScrollExtent - 200) {
      _loadMoreTracks();
    }
  }

  Future<void> _loadTracks() async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
    });

    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 500));

    setState(() {
      _tracks = MockData.mockTracks;
      _isLoading = false;
    });
  }

  Future<void> _loadMoreTracks() async {
    if (_isLoading || !_hasMore) return;

    setState(() {
      _isLoading = true;
    });

    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 500));

    // Add more tracks (duplicating for demo)
    final moreTracks = MockData.mockTracks.map((track) => 
      track.copyWith(id: '${track.id}_${DateTime.now().millisecondsSinceEpoch}')
    ).toList();

    setState(() {
      _tracks.addAll(moreTracks);
      _isLoading = false;
      // Stop loading more after 3 pages for demo
      if (_tracks.length > 18) {
        _hasMore = false;
      }
    });
  }

  Future<void> _onRefresh() async {
    setState(() {
      _tracks.clear();
      _hasMore = true;
    });
    await _loadTracks();
  }

  void _onTrackTap(Track track) {
    context.goToTrackDetail(track.id);
  }

  void _onPlayTap(Track track) {
    // Handle play/pause
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Playing: ${track.title}')),
    );
  }

  void _onLikeTap(Track track) {
    setState(() {
      final index = _tracks.indexWhere((t) => t.id == track.id);
      if (index != -1) {
        _tracks[index] = track.copyWith(
          isLiked: !track.isLiked,
          likeCount: track.isLiked ? track.likeCount - 1 : track.likeCount + 1,
        );
      }
    });
  }

  void _onRepostTap(Track track) {
    setState(() {
      final index = _tracks.indexWhere((t) => t.id == track.id);
      if (index != -1) {
        _tracks[index] = track.copyWith(
          isReposted: !track.isReposted,
          repostCount: track.isReposted ? track.repostCount - 1 : track.repostCount + 1,
        );
      }
    });
  }

  void _onCommentTap(Track track) {
    context.goToTrackDetail(track.id);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          AppConstants.appName,
          style: Theme.of(context).textTheme.headlineMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        actions: [
          IconButton(
            onPressed: () => context.goToSettings(),
            icon: const Icon(Icons.settings_outlined),
          ),
          Padding(
            padding: const EdgeInsets.only(right: 8),
            child: UserAvatar(
              imageUrl: 'https://picsum.photos/200/200?random=100',
              size: 32,
              onTap: () => context.goToProfile(),
            ),
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: _onRefresh,
        child: _tracks.isEmpty && _isLoading
            ? const Center(child: CircularProgressIndicator())
            : CustomScrollView(
                controller: _scrollController,
                slivers: [
                  // Header section
                  SliverToBoxAdapter(
                    child: Padding(
                      padding: const EdgeInsets.all(AppConstants.defaultPadding),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Your Feed',
                            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'Discover new music from artists you follow',
                            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  
                  // Tracks list
                  SliverList(
                    delegate: SliverChildBuilderDelegate(
                      (context, index) {
                        if (index >= _tracks.length) {
                          return _hasMore
                              ? const Padding(
                                  padding: EdgeInsets.all(16),
                                  child: Center(child: CircularProgressIndicator()),
                                )
                              : const SizedBox.shrink();
                        }

                        final track = _tracks[index];
                        return TrackCard(
                          track: track,
                          onTap: () => _onTrackTap(track),
                          onPlay: () => _onPlayTap(track),
                          onLike: () => _onLikeTap(track),
                          onRepost: () => _onRepostTap(track),
                          onComment: () => _onCommentTap(track),
                        );
                      },
                      childCount: _tracks.length + (_hasMore ? 1 : 0),
                    ),
                  ),
                  
                  // Bottom padding for mini player
                  const SliverToBoxAdapter(
                    child: SizedBox(height: 80),
                  ),
                ],
              ),
      ),
    );
  }
}
