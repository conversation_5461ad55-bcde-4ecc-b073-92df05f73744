class AppConstants {
  // App Info
  static const String appName = 'SoundStream';
  static const String appVersion = '1.0.0';
  
  // Colors
  static const int primaryOrange = 0xFFFF5722;
  static const int darkBackground = 0xFF121212;
  static const int darkSurface = 0xFF1E1E1E;
  static const int darkCard = 0xFF2A2A2A;
  static const int lightGrey = 0xFF9E9E9E;
  static const int darkGrey = 0xFF424242;
  
  // Dimensions
  static const double defaultPadding = 16.0;
  static const double smallPadding = 8.0;
  static const double largePadding = 24.0;
  static const double borderRadius = 12.0;
  static const double cardElevation = 4.0;
  
  // Animation Durations
  static const Duration shortAnimation = Duration(milliseconds: 200);
  static const Duration mediumAnimation = Duration(milliseconds: 400);
  static const Duration longAnimation = Duration(milliseconds: 600);
  
  // Audio Player
  static const double miniPlayerHeight = 60.0;
  static const double fullPlayerHeight = 400.0;
  
  // API (Mock for now)
  static const String baseUrl = 'https://api.soundstream.com';
  static const String apiVersion = 'v1';
  
  // Storage Keys
  static const String userTokenKey = 'user_token';
  static const String userDataKey = 'user_data';
  static const String themeKey = 'theme_mode';
  static const String playlistKey = 'saved_playlists';
  
  // Routes
  static const String splashRoute = '/';
  static const String loginRoute = '/login';
  static const String signupRoute = '/signup';
  static const String homeRoute = '/home';
  static const String searchRoute = '/search';
  static const String profileRoute = '/profile';
  static const String settingsRoute = '/settings';
  static const String playerRoute = '/player';
  static const String trackDetailRoute = '/track';
}

class AppStrings {
  // General
  static const String appName = 'SoundStream';
  static const String loading = 'Loading...';
  static const String error = 'Something went wrong';
  static const String retry = 'Retry';
  static const String cancel = 'Cancel';
  static const String save = 'Save';
  static const String delete = 'Delete';
  static const String edit = 'Edit';
  static const String share = 'Share';
  
  // Auth
  static const String login = 'Login';
  static const String signup = 'Sign Up';
  static const String logout = 'Logout';
  static const String email = 'Email';
  static const String password = 'Password';
  static const String confirmPassword = 'Confirm Password';
  static const String forgotPassword = 'Forgot Password?';
  static const String loginWithGoogle = 'Continue with Google';
  static const String loginWithApple = 'Continue with Apple';
  static const String alreadyHaveAccount = 'Already have an account?';
  static const String dontHaveAccount = "Don't have an account?";
  
  // Navigation
  static const String home = 'Home';
  static const String search = 'Search';
  static const String library = 'Library';
  static const String profile = 'Profile';
  static const String settings = 'Settings';
  
  // Player
  static const String nowPlaying = 'Now Playing';
  static const String play = 'Play';
  static const String pause = 'Pause';
  static const String next = 'Next';
  static const String previous = 'Previous';
  static const String shuffle = 'Shuffle';
  static const String repeat = 'Repeat';
  static const String like = 'Like';
  static const String unlike = 'Unlike';
  static const String repost = 'Repost';
  static const String comment = 'Comment';
  static const String addToPlaylist = 'Add to Playlist';
  
  // Search
  static const String searchHint = 'Search for tracks, artists, playlists...';
  static const String recentSearches = 'Recent Searches';
  static const String noResults = 'No results found';
  static const String tracks = 'Tracks';
  static const String artists = 'Artists';
  static const String playlists = 'Playlists';
  
  // Profile
  static const String followers = 'Followers';
  static const String following = 'Following';
  static const String follow = 'Follow';
  static const String unfollow = 'Unfollow';
  static const String editProfile = 'Edit Profile';
  static const String yourTracks = 'Your Tracks';
  static const String yourPlaylists = 'Your Playlists';
  static const String likedTracks = 'Liked Tracks';
  

  
  // Settings
  static const String account = 'Account';
  static const String notifications = 'Notifications';
  static const String privacy = 'Privacy';
  static const String about = 'About';
  static const String termsOfService = 'Terms of Service';
  static const String privacyPolicy = 'Privacy Policy';
  static const String version = 'Version';
}
