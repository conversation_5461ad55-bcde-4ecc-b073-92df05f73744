import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../presentation/screens/splash_screen.dart';
import '../../presentation/screens/auth/login_screen.dart';
import '../../presentation/screens/auth/signup_screen.dart';
import '../../presentation/screens/main/main_screen.dart';
import '../../presentation/screens/home/<USER>';
import '../../presentation/screens/search/search_screen.dart';
import '../../presentation/screens/profile/profile_screen.dart';
import '../../presentation/screens/upload/upload_screen.dart';
import '../../presentation/screens/settings/settings_screen.dart';
import '../../presentation/screens/player/player_screen.dart';
import '../../presentation/screens/track/track_detail_screen.dart';
import '../constants/app_constants.dart';

class AppRouter {
  static final GoRouter _router = GoRouter(
    initialLocation: AppConstants.splashRoute,
    debugLogDiagnostics: true,
    routes: [
      // Splash Screen
      GoRoute(
        path: AppConstants.splashRoute,
        name: 'splash',
        builder: (context, state) => const SplashScreen(),
      ),
      
      // Auth Routes
      GoRoute(
        path: AppConstants.loginRoute,
        name: 'login',
        builder: (context, state) => const LoginScreen(),
      ),
      GoRoute(
        path: AppConstants.signupRoute,
        name: 'signup',
        builder: (context, state) => const SignupScreen(),
      ),
      
      // Main App Shell with Bottom Navigation
      ShellRoute(
        builder: (context, state, child) => MainScreen(child: child),
        routes: [
          GoRoute(
            path: AppConstants.homeRoute,
            name: 'home',
            builder: (context, state) => const HomeScreen(),
          ),
          GoRoute(
            path: AppConstants.searchRoute,
            name: 'search',
            builder: (context, state) => const SearchScreen(),
          ),
          GoRoute(
            path: AppConstants.profileRoute,
            name: 'profile',
            builder: (context, state) {
              final userId = state.uri.queryParameters['userId'];
              return ProfileScreen(userId: userId);
            },
          ),
          GoRoute(
            path: AppConstants.uploadRoute,
            name: 'upload',
            builder: (context, state) => const UploadScreen(),
          ),
        ],
      ),
      
      // Full Screen Routes (outside main shell)
      GoRoute(
        path: AppConstants.playerRoute,
        name: 'player',
        builder: (context, state) {
          final trackId = state.uri.queryParameters['trackId'];
          return PlayerScreen(trackId: trackId);
        },
      ),
      GoRoute(
        path: '${AppConstants.trackDetailRoute}/:trackId',
        name: 'trackDetail',
        builder: (context, state) {
          final trackId = state.pathParameters['trackId']!;
          return TrackDetailScreen(trackId: trackId);
        },
      ),
      GoRoute(
        path: AppConstants.settingsRoute,
        name: 'settings',
        builder: (context, state) => const SettingsScreen(),
      ),
    ],
    errorBuilder: (context, state) => Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            Text(
              'Page not found',
              style: Theme.of(context).textTheme.headlineMedium,
            ),
            const SizedBox(height: 8),
            Text(
              'The page you are looking for does not exist.',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () => context.go(AppConstants.homeRoute),
              child: const Text('Go Home'),
            ),
          ],
        ),
      ),
    ),
  );

  static GoRouter get router => _router;
}

// Extension for easier navigation
extension AppRouterExtension on BuildContext {
  void goToHome() => go(AppConstants.homeRoute);
  void goToLogin() => go(AppConstants.loginRoute);
  void goToSignup() => go(AppConstants.signupRoute);
  void goToSearch() => go(AppConstants.searchRoute);
  void goToProfile([String? userId]) {
    if (userId != null) {
      go('${AppConstants.profileRoute}?userId=$userId');
    } else {
      go(AppConstants.profileRoute);
    }
  }
  void goToUpload() => go(AppConstants.uploadRoute);
  void goToSettings() => go(AppConstants.settingsRoute);
  void goToPlayer([String? trackId]) {
    if (trackId != null) {
      go('${AppConstants.playerRoute}?trackId=$trackId');
    } else {
      go(AppConstants.playerRoute);
    }
  }
  void goToTrackDetail(String trackId) => go('${AppConstants.trackDetailRoute}/$trackId');
}
