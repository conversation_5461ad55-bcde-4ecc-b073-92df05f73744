import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../../core/constants/app_constants.dart';

class SettingsScreen extends StatelessWidget {
  const SettingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(AppStrings.settings),
        leading: IconButton(
          onPressed: () => context.pop(),
          icon: const Icon(Icons.arrow_back),
        ),
      ),
      body: ListView(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        children: [
          _buildSection(
            context,
            title: AppStrings.account,
            items: [
              _SettingsItem(
                icon: Icons.person_outline,
                title: 'Edit Profile',
                onTap: () {},
              ),
              _SettingsItem(
                icon: Icons.security,
                title: 'Privacy & Security',
                onTap: () {},
              ),
              _SettingsItem(
                icon: Icons.notifications_outlined,
                title: AppStrings.notifications,
                onTap: () {},
              ),
            ],
          ),
          
          const SizedBox(height: 24),
          
          _buildSection(
            context,
            title: 'Audio',
            items: [
              _SettingsItem(
                icon: Icons.volume_up_outlined,
                title: 'Audio Quality',
                subtitle: 'High',
                onTap: () {},
              ),
              _SettingsItem(
                icon: Icons.download_outlined,
                title: 'Download Quality',
                subtitle: 'High',
                onTap: () {},
              ),
              _SettingsItem(
                icon: Icons.equalizer,
                title: 'Equalizer',
                onTap: () {},
              ),
            ],
          ),
          
          const SizedBox(height: 24),
          
          _buildSection(
            context,
            title: 'Support',
            items: [
              _SettingsItem(
                icon: Icons.help_outline,
                title: 'Help Center',
                onTap: () {},
              ),
              _SettingsItem(
                icon: Icons.feedback_outlined,
                title: 'Send Feedback',
                onTap: () {},
              ),
              _SettingsItem(
                icon: Icons.bug_report_outlined,
                title: 'Report a Bug',
                onTap: () {},
              ),
            ],
          ),
          
          const SizedBox(height: 24),
          
          _buildSection(
            context,
            title: 'Legal',
            items: [
              _SettingsItem(
                icon: Icons.description_outlined,
                title: AppStrings.termsOfService,
                onTap: () {},
              ),
              _SettingsItem(
                icon: Icons.privacy_tip_outlined,
                title: AppStrings.privacyPolicy,
                onTap: () {},
              ),
              _SettingsItem(
                icon: Icons.info_outline,
                title: AppStrings.about,
                onTap: () {},
              ),
            ],
          ),
          
          const SizedBox(height: 24),
          
          _buildSection(
            context,
            title: 'App',
            items: [
              _SettingsItem(
                icon: Icons.info_outline,
                title: AppStrings.version,
                subtitle: AppConstants.appVersion,
                onTap: () {},
              ),
            ],
          ),
          
          const SizedBox(height: 32),
          
          // Logout button
          Card(
            child: ListTile(
              leading: Icon(
                Icons.logout,
                color: Theme.of(context).colorScheme.error,
              ),
              title: Text(
                AppStrings.logout,
                style: TextStyle(
                  color: Theme.of(context).colorScheme.error,
                  fontWeight: FontWeight.w500,
                ),
              ),
              onTap: () {
                _showLogoutDialog(context);
              },
            ),
          ),
          
          const SizedBox(height: 80), // Bottom padding
        ],
      ),
    );
  }

  Widget _buildSection(
    BuildContext context, {
    required String title,
    required List<_SettingsItem> items,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
            color: Theme.of(context).colorScheme.primary,
          ),
        ),
        const SizedBox(height: 8),
        Card(
          child: Column(
            children: items.map((item) {
              final isLast = items.last == item;
              return Column(
                children: [
                  ListTile(
                    leading: Icon(item.icon),
                    title: Text(item.title),
                    subtitle: item.subtitle != null ? Text(item.subtitle!) : null,
                    trailing: const Icon(Icons.chevron_right),
                    onTap: item.onTap,
                  ),
                  if (!isLast) const Divider(height: 1),
                ],
              );
            }).toList(),
          ),
        ),
      ],
    );
  }

  void _showLogoutDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Logout'),
        content: const Text('Are you sure you want to logout?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              context.go(AppConstants.loginRoute);
            },
            child: Text(
              'Logout',
              style: TextStyle(
                color: Theme.of(context).colorScheme.error,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class _SettingsItem {
  final IconData icon;
  final String title;
  final String? subtitle;
  final VoidCallback onTap;

  const _SettingsItem({
    required this.icon,
    required this.title,
    this.subtitle,
    required this.onTap,
  });
}
