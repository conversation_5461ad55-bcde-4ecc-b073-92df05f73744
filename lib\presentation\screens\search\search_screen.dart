import 'package:flutter/material.dart';
import '../../../core/constants/app_constants.dart';
import '../../../data/repositories/mock_data.dart';
import '../../../data/models/track.dart';
import '../../../data/models/user.dart';
import '../../widgets/common/track_card.dart';
import '../../widgets/common/user_avatar.dart';

class SearchScreen extends StatefulWidget {
  const SearchScreen({super.key});

  @override
  State<SearchScreen> createState() => _SearchScreenState();
}

class _SearchScreenState extends State<SearchScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final TextEditingController _searchController = TextEditingController();
  
  List<Track> _searchResults = [];
  List<User> _userResults = [];
  bool _isSearching = false;
  String _currentQuery = '';

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  void _performSearch(String query) async {
    if (query.isEmpty) {
      setState(() {
        _searchResults.clear();
        _userResults.clear();
        _currentQuery = '';
      });
      return;
    }

    setState(() {
      _isSearching = true;
      _currentQuery = query;
    });

    // Simulate search delay
    await Future.delayed(const Duration(milliseconds: 500));

    // Filter mock data based on query
    final tracks = MockData.mockTracks.where((track) =>
        track.title.toLowerCase().contains(query.toLowerCase()) ||
        track.artistName.toLowerCase().contains(query.toLowerCase()) ||
        track.genre.toLowerCase().contains(query.toLowerCase())).toList();

    final users = MockData.mockUsers.where((user) =>
        user.displayName.toLowerCase().contains(query.toLowerCase()) ||
        user.username.toLowerCase().contains(query.toLowerCase())).toList();

    setState(() {
      _searchResults = tracks;
      _userResults = users;
      _isSearching = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: TextField(
          controller: _searchController,
          decoration: InputDecoration(
            hintText: AppStrings.searchHint,
            border: InputBorder.none,
            prefixIcon: const Icon(Icons.search),
            suffixIcon: _searchController.text.isNotEmpty
                ? IconButton(
                    onPressed: () {
                      _searchController.clear();
                      _performSearch('');
                    },
                    icon: const Icon(Icons.clear),
                  )
                : null,
          ),
          onChanged: _performSearch,
          autofocus: true,
        ),
        bottom: _currentQuery.isNotEmpty
            ? TabBar(
                controller: _tabController,
                tabs: const [
                  Tab(text: AppStrings.tracks),
                  Tab(text: AppStrings.artists),
                  Tab(text: AppStrings.playlists),
                ],
              )
            : null,
      ),
      body: _currentQuery.isEmpty
          ? _buildEmptyState()
          : _isSearching
              ? const Center(child: CircularProgressIndicator())
              : TabBarView(
                  controller: _tabController,
                  children: [
                    _buildTracksTab(),
                    _buildArtistsTab(),
                    _buildPlaylistsTab(),
                  ],
                ),
    );
  }

  Widget _buildEmptyState() {
    return Padding(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        children: [
          // Recent searches
          if (MockData.mockSearchSuggestions.isNotEmpty) ...[
            Align(
              alignment: Alignment.centerLeft,
              child: Text(
                AppStrings.recentSearches,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            const SizedBox(height: 16),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: MockData.mockSearchSuggestions.take(10).map((suggestion) {
                return ActionChip(
                  label: Text(suggestion),
                  onPressed: () {
                    _searchController.text = suggestion;
                    _performSearch(suggestion);
                  },
                );
              }).toList(),
            ),
            const SizedBox(height: 32),
          ],
          
          // Trending genres
          Align(
            alignment: Alignment.centerLeft,
            child: Text(
              'Trending Genres',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          const SizedBox(height: 16),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: MockData.mockGenres.take(8).map((genre) {
              return FilterChip(
                label: Text(genre),
                onSelected: (selected) {
                  if (selected) {
                    _searchController.text = genre;
                    _performSearch(genre);
                  }
                },
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildTracksTab() {
    if (_searchResults.isEmpty) {
      return const Center(
        child: Text(AppStrings.noResults),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.only(bottom: 80),
      itemCount: _searchResults.length,
      itemBuilder: (context, index) {
        final track = _searchResults[index];
        return TrackCard(
          track: track,
          onTap: () {
            // Navigate to track detail
          },
          onPlay: () {
            // Handle play
          },
          onLike: () {
            // Handle like
          },
          onRepost: () {
            // Handle repost
          },
          onComment: () {
            // Handle comment
          },
        );
      },
    );
  }

  Widget _buildArtistsTab() {
    if (_userResults.isEmpty) {
      return const Center(
        child: Text(AppStrings.noResults),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      itemCount: _userResults.length,
      itemBuilder: (context, index) {
        final user = _userResults[index];
        return UserAvatarWithName(
          imageUrl: user.avatarUrl,
          displayName: user.displayName,
          subtitle: '${user.formattedFollowersCount} followers • ${user.formattedTracksCount} tracks',
          onTap: () {
            // Navigate to user profile
          },
          trailing: OutlinedButton(
            onPressed: () {
              // Handle follow
            },
            child: Text(user.isFollowing ? 'Following' : 'Follow'),
          ),
        );
      },
    );
  }

  Widget _buildPlaylistsTab() {
    return const Center(
      child: Text('Playlists coming soon'),
    );
  }
}
