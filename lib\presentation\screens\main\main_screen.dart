import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../../core/constants/app_constants.dart';
import '../../widgets/player/mini_player.dart';

class MainScreen extends StatefulWidget {
  final Widget child;

  const MainScreen({
    super.key,
    required this.child,
  });

  @override
  State<MainScreen> createState() => _MainScreenState();
}

class _MainScreenState extends State<MainScreen> {
  int _selectedIndex = 0;

  final List<NavigationItem> _navigationItems = [
    NavigationItem(
      icon: Icons.home_outlined,
      selectedIcon: Icons.home,
      label: AppStrings.home,
      route: AppConstants.homeRoute,
    ),
    NavigationItem(
      icon: Icons.search_outlined,
      selectedIcon: Icons.search,
      label: AppStrings.search,
      route: AppConstants.searchRoute,
    ),
    NavigationItem(
      icon: Icons.library_music_outlined,
      selectedIcon: Icons.library_music,
      label: AppStrings.library,
      route: AppConstants.profileRoute,
    ),
    NavigationItem(
      icon: Icons.upload_outlined,
      selectedIcon: Icons.upload,
      label: AppStrings.upload,
      route: AppConstants.uploadRoute,
    ),
  ];

  void _onItemTapped(int index) {
    if (index != _selectedIndex) {
      setState(() {
        _selectedIndex = index;
      });
      context.go(_navigationItems[index].route);
    }
  }

  int _calculateSelectedIndex(String location) {
    for (int i = 0; i < _navigationItems.length; i++) {
      if (location.startsWith(_navigationItems[i].route)) {
        return i;
      }
    }
    return 0;
  }

  @override
  Widget build(BuildContext context) {
    final String location = GoRouterState.of(context).uri.path;
    final int selectedIndex = _calculateSelectedIndex(location);

    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, result) async {
        if (didPop) return;

        // If we're on the home screen, show exit dialog
        if (selectedIndex == 0) {
          final shouldExit = await _showExitDialog(context);
          if (shouldExit && context.mounted) {
            Navigator.of(context).pop();
          }
        } else {
          // Navigate to home screen instead of exiting
          context.go(AppConstants.homeRoute);
        }
      },
      child: Scaffold(
        body: Column(
          children: [
            // Main content
            Expanded(child: widget.child),

            // Mini player (when a track is playing)
            const MiniPlayer(),
          ],
        ),
        bottomNavigationBar: Container(
          decoration: BoxDecoration(
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 8,
                offset: const Offset(0, -2),
              ),
            ],
          ),
          child: BottomNavigationBar(
            type: BottomNavigationBarType.fixed,
            currentIndex: selectedIndex,
            onTap: _onItemTapped,
            backgroundColor: Theme.of(context).colorScheme.surface,
            selectedItemColor: Theme.of(context).colorScheme.primary,
            unselectedItemColor: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
            selectedFontSize: 12,
            unselectedFontSize: 12,
            items: _navigationItems.map((item) {
              final isSelected = selectedIndex == _navigationItems.indexOf(item);
              return BottomNavigationBarItem(
                icon: Icon(
                  isSelected ? item.selectedIcon : item.icon,
                  size: 24,
                ),
                label: item.label,
              );
            }).toList(),
          ),
        ),
      ),
    );
  }

  Future<bool> _showExitDialog(BuildContext context) async {
    return await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Exit App'),
        content: const Text('Are you sure you want to exit the app?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('Exit'),
          ),
        ],
      ),
    ) ?? false;
  }
}

class NavigationItem {
  final IconData icon;
  final IconData selectedIcon;
  final String label;
  final String route;

  const NavigationItem({
    required this.icon,
    required this.selectedIcon,
    required this.label,
    required this.route,
  });
}
