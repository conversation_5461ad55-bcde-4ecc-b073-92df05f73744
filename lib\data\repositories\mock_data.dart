import '../models/track.dart';
import '../models/user.dart';

class MockData {
  static List<User> get mockUsers => [
    User(
      id: '1',
      username: 'john_beats',
      displayName: '<PERSON>',
      email: '<EMAIL>',
      bio: 'Electronic music producer from LA 🎵',
      avatarUrl: 'https://picsum.photos/200/200?random=1',
      bannerUrl: 'https://picsum.photos/800/300?random=1',
      location: 'Los Angeles, CA',
      website: 'https://johnbeats.com',
      followersCount: 15420,
      followingCount: 892,
      tracksCount: 47,
      likesCount: 2341,
      createdAt: DateTime.now().subtract(const Duration(days: 365)),
      isVerified: true,
    ),
    User(
      id: '2',
      username: 'sarah_vocals',
      displayName: '<PERSON>',
      email: '<EMAIL>',
      bio: 'Singer-songwriter | Indie pop vibes ✨',
      avatarUrl: 'https://picsum.photos/200/200?random=2',
      bannerUrl: 'https://picsum.photos/800/300?random=2',
      location: 'Nashville, TN',
      followersCount: 8934,
      followingCount: 456,
      tracksCount: 23,
      likesCount: 1876,
      createdAt: DateTime.now().subtract(const Duration(days: 200)),
      isVerified: false,
    ),
    User(
      id: '3',
      username: 'mike_drums',
      displayName: 'Mike Drums',
      email: '<EMAIL>',
      bio: 'Drummer & Producer | Hip-hop beats 🥁',
      avatarUrl: 'https://picsum.photos/200/200?random=3',
      bannerUrl: 'https://picsum.photos/800/300?random=3',
      location: 'Atlanta, GA',
      followersCount: 12567,
      followingCount: 234,
      tracksCount: 89,
      likesCount: 4521,
      createdAt: DateTime.now().subtract(const Duration(days: 500)),
      isVerified: true,
    ),
  ];

  static List<Track> get mockTracks => [
    Track(
      id: '1',
      title: 'Midnight Vibes',
      artistName: 'John Beats',
      artistId: '1',
      description: 'A chill electronic track perfect for late night drives',
      artworkUrl: 'https://picsum.photos/400/400?random=11',
      audioUrl: 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav',
      duration: const Duration(minutes: 3, seconds: 45),
      playCount: 12450,
      likeCount: 892,
      repostCount: 156,
      commentCount: 43,
      createdAt: DateTime.now().subtract(const Duration(days: 7)),
      tags: ['electronic', 'chill', 'ambient'],
      genre: 'Electronic',
      isLiked: true,
    ),
    Track(
      id: '2',
      title: 'Summer Dreams',
      artistName: 'Sarah Vocals',
      artistId: '2',
      description: 'An uplifting indie pop song about chasing dreams',
      artworkUrl: 'https://picsum.photos/400/400?random=12',
      audioUrl: 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav',
      duration: const Duration(minutes: 4, seconds: 12),
      playCount: 8934,
      likeCount: 567,
      repostCount: 89,
      commentCount: 78,
      createdAt: DateTime.now().subtract(const Duration(days: 14)),
      tags: ['indie', 'pop', 'uplifting'],
      genre: 'Indie Pop',
      isLiked: false,
    ),
    Track(
      id: '3',
      title: 'Urban Rhythm',
      artistName: 'Mike Drums',
      artistId: '3',
      description: 'Heavy hip-hop beat with urban influences',
      artworkUrl: 'https://picsum.photos/400/400?random=13',
      audioUrl: 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav',
      duration: const Duration(minutes: 2, seconds: 58),
      playCount: 15678,
      likeCount: 1234,
      repostCount: 234,
      commentCount: 156,
      createdAt: DateTime.now().subtract(const Duration(days: 3)),
      tags: ['hip-hop', 'urban', 'beats'],
      genre: 'Hip-Hop',
      isLiked: true,
    ),
    Track(
      id: '4',
      title: 'Ocean Waves',
      artistName: 'John Beats',
      artistId: '1',
      description: 'Relaxing ambient sounds mixed with electronic elements',
      artworkUrl: 'https://picsum.photos/400/400?random=14',
      audioUrl: 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav',
      duration: const Duration(minutes: 5, seconds: 23),
      playCount: 9876,
      likeCount: 654,
      repostCount: 98,
      commentCount: 32,
      createdAt: DateTime.now().subtract(const Duration(days: 21)),
      tags: ['ambient', 'relaxing', 'nature'],
      genre: 'Ambient',
      isLiked: false,
    ),
    Track(
      id: '5',
      title: 'Neon Lights',
      artistName: 'Sarah Vocals',
      artistId: '2',
      description: 'Synthwave inspired track with retro vibes',
      artworkUrl: 'https://picsum.photos/400/400?random=15',
      audioUrl: 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav',
      duration: const Duration(minutes: 3, seconds: 56),
      playCount: 11234,
      likeCount: 789,
      repostCount: 145,
      commentCount: 67,
      createdAt: DateTime.now().subtract(const Duration(days: 10)),
      tags: ['synthwave', 'retro', 'electronic'],
      genre: 'Synthwave',
      isLiked: true,
    ),
    Track(
      id: '6',
      title: 'Street Symphony',
      artistName: 'Mike Drums',
      artistId: '3',
      description: 'Complex drum patterns with orchestral elements',
      artworkUrl: 'https://picsum.photos/400/400?random=16',
      audioUrl: 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav',
      duration: const Duration(minutes: 4, seconds: 34),
      playCount: 13567,
      likeCount: 987,
      repostCount: 178,
      commentCount: 89,
      createdAt: DateTime.now().subtract(const Duration(days: 5)),
      tags: ['orchestral', 'drums', 'complex'],
      genre: 'Experimental',
      isLiked: false,
    ),
  ];

  static List<String> get mockGenres => [
    'Electronic',
    'Hip-Hop',
    'Indie Pop',
    'Rock',
    'Jazz',
    'Classical',
    'Ambient',
    'Synthwave',
    'House',
    'Techno',
    'R&B',
    'Folk',
    'Country',
    'Reggae',
    'Punk',
    'Metal',
  ];

  static List<String> get mockSearchSuggestions => [
    'midnight vibes',
    'summer dreams',
    'urban rhythm',
    'john beats',
    'sarah vocals',
    'mike drums',
    'electronic music',
    'hip hop beats',
    'indie pop',
    'ambient sounds',
  ];
}
