import 'package:equatable/equatable.dart';

class Track extends Equatable {
  final String id;
  final String title;
  final String artistName;
  final String artistId;
  final String? description;
  final String? artworkUrl;
  final String audioUrl;
  final Duration duration;
  final int playCount;
  final int likeCount;
  final int repostCount;
  final int commentCount;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final List<String> tags;
  final String genre;
  final bool isLiked;
  final bool isReposted;
  final bool isPublic;

  const Track({
    required this.id,
    required this.title,
    required this.artistName,
    required this.artistId,
    this.description,
    this.artworkUrl,
    required this.audioUrl,
    required this.duration,
    this.playCount = 0,
    this.likeCount = 0,
    this.repostCount = 0,
    this.commentCount = 0,
    required this.createdAt,
    this.updatedAt,
    this.tags = const [],
    this.genre = '',
    this.isLiked = false,
    this.isReposted = false,
    this.isPublic = true,
  });

  Track copyWith({
    String? id,
    String? title,
    String? artistName,
    String? artistId,
    String? description,
    String? artworkUrl,
    String? audioUrl,
    Duration? duration,
    int? playCount,
    int? likeCount,
    int? repostCount,
    int? commentCount,
    DateTime? createdAt,
    DateTime? updatedAt,
    List<String>? tags,
    String? genre,
    bool? isLiked,
    bool? isReposted,
    bool? isPublic,
  }) {
    return Track(
      id: id ?? this.id,
      title: title ?? this.title,
      artistName: artistName ?? this.artistName,
      artistId: artistId ?? this.artistId,
      description: description ?? this.description,
      artworkUrl: artworkUrl ?? this.artworkUrl,
      audioUrl: audioUrl ?? this.audioUrl,
      duration: duration ?? this.duration,
      playCount: playCount ?? this.playCount,
      likeCount: likeCount ?? this.likeCount,
      repostCount: repostCount ?? this.repostCount,
      commentCount: commentCount ?? this.commentCount,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      tags: tags ?? this.tags,
      genre: genre ?? this.genre,
      isLiked: isLiked ?? this.isLiked,
      isReposted: isReposted ?? this.isReposted,
      isPublic: isPublic ?? this.isPublic,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'artistName': artistName,
      'artistId': artistId,
      'description': description,
      'artworkUrl': artworkUrl,
      'audioUrl': audioUrl,
      'duration': duration.inMilliseconds,
      'playCount': playCount,
      'likeCount': likeCount,
      'repostCount': repostCount,
      'commentCount': commentCount,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
      'tags': tags,
      'genre': genre,
      'isLiked': isLiked,
      'isReposted': isReposted,
      'isPublic': isPublic,
    };
  }

  factory Track.fromJson(Map<String, dynamic> json) {
    return Track(
      id: json['id'] as String,
      title: json['title'] as String,
      artistName: json['artistName'] as String,
      artistId: json['artistId'] as String,
      description: json['description'] as String?,
      artworkUrl: json['artworkUrl'] as String?,
      audioUrl: json['audioUrl'] as String,
      duration: Duration(milliseconds: json['duration'] as int),
      playCount: json['playCount'] as int? ?? 0,
      likeCount: json['likeCount'] as int? ?? 0,
      repostCount: json['repostCount'] as int? ?? 0,
      commentCount: json['commentCount'] as int? ?? 0,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] != null 
          ? DateTime.parse(json['updatedAt'] as String) 
          : null,
      tags: List<String>.from(json['tags'] as List? ?? []),
      genre: json['genre'] as String? ?? '',
      isLiked: json['isLiked'] as bool? ?? false,
      isReposted: json['isReposted'] as bool? ?? false,
      isPublic: json['isPublic'] as bool? ?? true,
    );
  }

  String get formattedDuration {
    final minutes = duration.inMinutes;
    final seconds = duration.inSeconds % 60;
    return '${minutes.toString().padLeft(1, '0')}:${seconds.toString().padLeft(2, '0')}';
  }

  String get formattedPlayCount {
    if (playCount >= 1000000) {
      return '${(playCount / 1000000).toStringAsFixed(1)}M';
    } else if (playCount >= 1000) {
      return '${(playCount / 1000).toStringAsFixed(1)}K';
    }
    return playCount.toString();
  }

  String get formattedLikeCount {
    if (likeCount >= 1000000) {
      return '${(likeCount / 1000000).toStringAsFixed(1)}M';
    } else if (likeCount >= 1000) {
      return '${(likeCount / 1000).toStringAsFixed(1)}K';
    }
    return likeCount.toString();
  }

  @override
  List<Object?> get props => [
        id,
        title,
        artistName,
        artistId,
        description,
        artworkUrl,
        audioUrl,
        duration,
        playCount,
        likeCount,
        repostCount,
        commentCount,
        createdAt,
        updatedAt,
        tags,
        genre,
        isLiked,
        isReposted,
        isPublic,
      ];
}
